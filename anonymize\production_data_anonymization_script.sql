-- =====================================================================================================================
-- PRODUCTION DATA ANONYMIZATION SCRIPT
-- =====================================================================================================================
-- This script anonymizes sensitive personal data in the production database according to GDPR and data privacy requirements.
-- 
-- IMPORTANT WARNINGS:
-- 1. This script will PERMANENTLY modify data - ensure you have a complete backup before running
-- 2. Test this script thoroughly in a non-production environment first
-- 3. Review all anonymization logic to ensure it meets your specific compliance requirements
-- 4. Consider running during maintenance windows due to potential locking
--
-- Performance Optimizations:
-- - Uses bulk UPDATE operations instead of row-by-row processing
-- - Leverages efficient JOIN operations for data generation
-- - Implements transaction boundaries to allow rollback if needed
-- - Uses WITH (NOLOCK) hints where appropriate for read operations
-- =====================================================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON; -- Ensures automatic rollback on any error

DECLARE @StartTime DATETIME = GETDATE();
DECLARE @RowCount INT;
DECLARE @ErrorMessage NVARCHAR(4000);

PRINT '=== PRODUCTION DATA ANONYMIZATION STARTED AT ' + CONVERT(VARCHAR, @StartTime, 120) + ' ===';
PRINT '';

BEGIN TRY
    BEGIN TRANSACTION AnonymizationTransaction;
    
    -- =====================================================================================================================
    -- STEP 1: INITIALIZE ANONYMIZATION FUNCTIONS
    -- =====================================================================================================================
    
    PRINT 'Step 1: Initializing anonymization functions...';
    
    -- Helper function definitions (embedded in UPDATE statements for better performance)
    -- Names will be generated using modular arithmetic on CHECKSUM for consistency
    -- Company names will be generated using predefined patterns
    -- All data generation will be done directly in UPDATE statements
    
    PRINT 'Anonymization functions initialized successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 2: ANONYMIZE PRIMARY PERSONAL INFORMATION TABLES
    -- =====================================================================================================================
    
    PRINT 'Step 2: Anonymizing primary personal information tables...';
    
    -- Anonymize Person table
    PRINT '  - Anonymizing Person table...';
    UPDATE Person SET
        FirstName = 'FirstName_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'FNAME', GETDATE()))) % 100000 AS VARCHAR(10)),
        LastName = 'LastName_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'LNAME', GETDATE()))) % 100000 AS VARCHAR(10)),
        Email = CASE 
            WHEN Email IS NOT NULL THEN 
                'user' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local'
            ELSE NULL 
        END,
        Phone = CASE 
            WHEN Phone IS NOT NULL THEN 
                '******-' + RIGHT('000' + CAST(ABS(CHECKSUM(NEWID())) % 10000 AS VARCHAR(4)), 4)
            ELSE NULL 
        END,
        Notes = CASE 
            WHEN Notes IS NOT NULL THEN 'Anonymized note entry'
            ELSE NULL 
        END,
        CustomerName = CASE 
            WHEN CustomerName IS NOT NULL THEN 'Anonymized Customer'
            ELSE NULL 
        END,
        LastMedicalDate = CASE 
            WHEN LastMedicalDate IS NOT NULL THEN 
                DATEADD(day, ABS(CHECKSUM(NEWID())) % 365, '2020-01-01')
            ELSE NULL 
        END,
        Photo = NULL,
        PhotoFileSize = NULL,
        PhotoInternalName = NULL;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Person table.';
    
    -- Anonymize ContactPersonInformation table
    PRINT '  - Anonymizing ContactPersonInformation table...';
    UPDATE ContactPersonInformation SET
        FirstName = 'Contact_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'CFNAME'))) % 100000 AS VARCHAR(10)),
        LastName = 'Person_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'CLNAME'))) % 100000 AS VARCHAR(10)),
        Email = 'contact' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local',
        PhoneNo = '******-' + RIGHT('000' + CAST(ABS(CHECKSUM(NEWID())) % 10000 AS VARCHAR(4)), 4),
        Address = CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'ADDRESS'))) % 9999 + 1 AS VARCHAR(4)) + 
                  ' Street_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'STREET'))) % 1000 AS VARCHAR(4)) + 
                  ', City_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'CITY'))) % 1000 AS VARCHAR(4)) + 
                  ', ST ' + RIGHT('0000' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'ZIP'))) % 100000 AS VARCHAR(5)), 5);
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in ContactPersonInformation table.';
    
    -- Anonymize GOSecurity.GOUser table
    PRINT '  - Anonymizing GOSecurity.GOUser table...';
    UPDATE [GOSecurity].[GOUser] SET
        UserName = 'User_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'UNAME'))) % 1000000 AS VARCHAR(10)),
        EmailAddress = 'user_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'EMAIL'))) % 1000000 AS VARCHAR(10)) + '@anonymized.local',
        NewEmailAddress = CASE 
            WHEN NewEmailAddress IS NOT NULL THEN 
                'newuser_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'NEWEMAIL'))) % 1000000 AS VARCHAR(10)) + '@anonymized.local'
            ELSE NULL 
        END,
        FullName = 'FullName_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'FULLNAME'))) % 1000000 AS VARCHAR(10)),
        FirstName = 'First_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'GFNAME'))) % 100000 AS VARCHAR(10)),
        LastName = 'Last_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'GLNAME'))) % 100000 AS VARCHAR(10)),
        Password = 'ANONYMIZED_PASSWORD_HASH_' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(10)),
        ExternalUserId = CASE 
            WHEN ExternalUserId IS NOT NULL THEN 
                'ext_' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(10))
            ELSE NULL 
        END,
        -- PRESERVE CRITICAL ACCESS FLAGS TO ALLOW TEST USER LOGIN
        EmailValidated = 1,              -- Enable email validation for login
        UserValidated = 1,               -- Enable user validation for login
        Blocked = 0,                     -- Unblock users for login
        Unregistered = 0,                -- Mark as registered users
        EmailChangeValidationInProgress = 0  -- Clear any pending email validation;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOSecurity.GOUser table.';
    
    -- Anonymize WebsiteUser table
    PRINT '  - Anonymizing WebsiteUser table...';
    UPDATE WebsiteUser SET
        Username = 'webuser' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)),
        Password = 'ANONYMIZED_WEB_PASSWORD_' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(10)),
        -- PRESERVE ACTIVE STATUS FOR TEST ACCESS
        Active = 1;                      -- Keep users active for login
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in WebsiteUser table.';
    
    PRINT 'Step 2 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 3: ANONYMIZE SECONDARY PERSONAL INFORMATION
    -- =====================================================================================================================
    
    PRINT 'Step 3: Anonymizing secondary personal information...';
    
    -- Anonymize Customer table
    PRINT '  - Anonymizing Customer table...';
    UPDATE Customer SET
        ContactNumber = CASE 
            WHEN ContactNumber IS NOT NULL THEN 
                '******-' + RIGHT('000' + CAST(ABS(CHECKSUM(NEWID())) % 10000 AS VARCHAR(4)), 4)
            ELSE NULL 
        END,
        Email = CASE 
            WHEN Email IS NOT NULL THEN 
                'customer' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local'
            ELSE NULL 
        END,
        Addess = CASE 
            WHEN Addess IS NOT NULL THEN 
                CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'CUSTADDR'))) % 9999 + 1 AS VARCHAR(4)) + 
                ' BusinessSt_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'STREET'))) % 1000 AS VARCHAR(4)) + 
                ', Corp_City_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'CITY'))) % 1000 AS VARCHAR(4)) + 
                ', ST ' + RIGHT('0000' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'ZIP'))) % 100000 AS VARCHAR(5)), 5)
            ELSE NULL 
        END,
        CompanyName = 'Company_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'COMPANY'))) % 1000000 AS VARCHAR(10));
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Customer table.';
    
    -- Anonymize Card table
    PRINT '  - Anonymizing Card table...';
    UPDATE Card SET
        CardNumber = 'CARD_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'CARDNUM'))) % 10000000 AS VARCHAR(10)),
        Weigand = CASE 
            WHEN Weigand IS NOT NULL THEN 
                CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'WEIGAND'))) % 100000 AS VARCHAR(10))
            ELSE NULL 
        END,
        FacilityCode = CASE 
            WHEN FacilityCode IS NOT NULL THEN 
                RIGHT('00' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'FACILITY'))) % 1000 AS VARCHAR(3)), 3)
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Card table.';
    
    -- Anonymize LicenceDetail table
    PRINT '  - Anonymizing LicenceDetail table...';
    UPDATE LicenceDetail SET
        LicenseNumber = CASE 
            WHEN LicenseNumber IS NOT NULL THEN 
                'LIC_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'LICENSE'))) % 100000000 AS VARCHAR(10))
            ELSE NULL 
        END,
        Document = NULL,
        DocumentFileSize = NULL,
        DocumentInternalName = NULL;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in LicenceDetail table.';
    
    -- Anonymize Module table
    PRINT '  - Anonymizing Module table...';
    UPDATE Module SET
        SimCardNumber = CASE 
            WHEN SimCardNumber IS NOT NULL THEN 
                'SIM_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'SIMCARD'))) % 1000000000000000 AS VARCHAR(20))
            ELSE NULL 
        END,
        CCID = CASE 
            WHEN CCID IS NOT NULL THEN 
                'CCID_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'CCIDNUM'))) % 1000000000000000 AS VARCHAR(20))
            ELSE NULL 
        END,
        TechNumber = CASE 
            WHEN TechNumber IS NOT NULL THEN 
                'TECH_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'TECHNUM'))) % 1000000 AS VARCHAR(10))
            ELSE NULL 
        END,
        Note = CASE 
            WHEN Note IS NOT NULL THEN 'Anonymized technical note'
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Module table.';
    
    -- Anonymize GOSecurity.GOUser2FA table
    PRINT '  - Anonymizing GOSecurity.GOUser2FA table...';
    UPDATE [GOSecurity].[GOUser2FA] SET
        OTPSecret = CASE 
            WHEN OTPSecret IS NOT NULL THEN 
                'OTP_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'OTPSECRET'))) % 1000000 AS VARCHAR(10))
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOSecurity.GOUser2FA table.';
    
    -- Anonymize GOSecurity.GOLoginHistory table
    PRINT '  - Anonymizing GOSecurity.GOLoginHistory table...';
    UPDATE [GOSecurity].[GOLoginHistory] SET
        [User] = 'LogUser_' + CAST(ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(10)), 'LOGUSER'))) % 100000 AS VARCHAR(10)),
        Info = 'Anonymized login info';
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOSecurity.GOLoginHistory table.';
    
    PRINT 'Step 3 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 4: ANONYMIZE LOCATION AND ACTIVITY DATA
    -- =====================================================================================================================
    
    PRINT 'Step 4: Anonymizing location and activity data...';
    
    -- Anonymize GPSHistory table - offset coordinates by deterministic amounts based on record ID
    PRINT '  - Anonymizing GPSHistory table...';
    UPDATE GPSHistory SET
        Latitude = CASE 
            WHEN Latitude IS NOT NULL THEN 
                40 + (ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'LAT'))) % 1000) / 1000.0
            ELSE NULL 
        END,
        Longitude = CASE 
            WHEN Longitude IS NOT NULL THEN 
                -100 + (ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'LON'))) % 1000) / 1000.0
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GPSHistory table.';
    
    -- Anonymize VehicleGPS table - offset coordinates by deterministic amounts based on record ID
    PRINT '  - Anonymizing VehicleGPS table...';
    UPDATE VehicleGPS SET
        Latitude = 40 + (ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'VLAT'))) % 1000) / 1000.0,
        Longitude = -100 + (ABS(CHECKSUM(CONCAT(CAST(Id AS VARCHAR(36)), 'VLON'))) % 1000) / 1000.0;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in VehicleGPS table.';
    
    -- Note: Session and Impact tables contain behavioral data but not direct personal identifiers
    -- The foreign key relationships will maintain referential integrity while the linked personal data is anonymized
    
    PRINT 'Step 4 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 5: ANONYMIZE AUDIT AND TRACKING INFORMATION
    -- =====================================================================================================================
    
    PRINT 'Step 5: Anonymizing audit and tracking information...';
    
    -- Anonymize GOChangeTracking.CustomerAudit table
    -- Note: Setting user references to NULL removes audit trail links while maintaining data privacy compliance
    PRINT '  - Anonymizing GOChangeTracking.CustomerAudit table...';
    IF EXISTS (SELECT 1 FROM sys.tables t JOIN sys.schemas s ON t.schema_id = s.schema_id 
               WHERE s.name = 'GOChangeTracking' AND t.name = 'CustomerAudit')
    BEGIN
        -- Update CustomerAudit by setting all user references to NULL (removes audit trail user links)
        -- This approach is simpler, faster, and eliminates foreign key constraint issues
        UPDATE [GOChangeTracking].[CustomerAudit] SET
            CreatedBy = NULL,
            LastModifiedBy = NULL,
            DeletedBy = NULL;
        
        SET @RowCount = @@ROWCOUNT;
        PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOChangeTracking.CustomerAudit table (set user references to NULL).';
    END
    ELSE
    BEGIN
        PRINT '    GOChangeTracking.CustomerAudit table not found - skipping.';
    END
    
    -- Anonymize GOChangeTracking.GOChangeDelta table
    PRINT '  - Anonymizing GOChangeTracking.GOChangeDelta table...';
    IF EXISTS (SELECT 1 FROM sys.tables t JOIN sys.schemas s ON t.schema_id = s.schema_id 
               WHERE s.name = 'GOChangeTracking' AND t.name = 'GOChangeDelta')
    BEGIN
        UPDATE [GOChangeTracking].[GOChangeDelta] SET
            Who = 'DeltaUser_' + CAST(ABS(CHECKSUM(CONCAT(CAST(GOChangeDeltaId AS VARCHAR(36)), 'DELTAUSER'))) % 100000 AS VARCHAR(10));
        
        SET @RowCount = @@ROWCOUNT;
        PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOChangeTracking.GOChangeDelta table.';
    END
    ELSE
    BEGIN
        PRINT '    GOChangeTracking.GOChangeDelta table not found - skipping.';
    END
    
    PRINT 'Step 5 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 6: PRESERVE CRITICAL ACCESS PERMISSIONS FOR TEST USERS
    -- =====================================================================================================================
    
    PRINT 'Step 6: Preserving critical access permissions for test users...';
    
    -- Ensure Person table access flags are properly set for testing
    PRINT '  - Setting Person table access flags for test access...';
    UPDATE Person SET
        VehicleAccess = 1,               -- Enable vehicle access
        WebSiteAccess = 1,               -- Enable website access  
        LicenseActive = 1,               -- Enable licensed features
        CanUnlockVehicle = 1,            -- Enable vehicle unlock capability
        MaintenanceMode = 0,             -- Disable maintenance mode
        NormalDriverAccess = 1,          -- Enable normal driver access
        OnDemand = 1;                    -- Enable on-demand features
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Person table for test access.';
    
    -- Note: User roles and permissions (GOUserRole, GOUserGroup, AccessGroup) are preserved
    -- These maintain the authorization structure while personal data is anonymized
    
    PRINT 'Step 6 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 7: CLEAN UP TEMPORARY OBJECTS
    -- =====================================================================================================================
    
    PRINT 'Step 7: Finalizing anonymization process...';
    
    -- No temporary objects to clean up - all anonymization done via direct UPDATE statements
    
    PRINT 'Anonymization process finalized successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- COMMIT TRANSACTION AND COMPLETION
    -- =====================================================================================================================
    
    COMMIT TRANSACTION AnonymizationTransaction;
    
    DECLARE @EndTime DATETIME = GETDATE();
    DECLARE @Duration VARCHAR(20) = CAST(DATEDIFF(SECOND, @StartTime, @EndTime) AS VARCHAR(10));
    
    PRINT '=== ANONYMIZATION COMPLETED SUCCESSFULLY ===';
    PRINT 'Start Time: ' + CONVERT(VARCHAR, @StartTime, 120);
    PRINT 'End Time: ' + CONVERT(VARCHAR, @EndTime, 120);
    PRINT 'Duration: ' + @Duration + ' seconds';
    PRINT '';
    PRINT 'All personal data has been anonymized according to the specified requirements.';
    PRINT 'Referential integrity has been maintained throughout the process.';
    
END TRY
BEGIN CATCH
    -- =====================================================================================================================
    -- ERROR HANDLING AND ROLLBACK
    -- =====================================================================================================================
    
    IF @@TRANCOUNT > 0
    BEGIN
        ROLLBACK TRANSACTION AnonymizationTransaction;
        PRINT 'TRANSACTION ROLLED BACK due to error.';
    END
    
    SET @ErrorMessage = ERROR_MESSAGE();
    DECLARE @ErrorNumber INT = ERROR_NUMBER();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    DECLARE @ErrorLine INT = ERROR_LINE();
    
    PRINT '';
    PRINT '=== ANONYMIZATION FAILED ===';
    PRINT 'Error Number: ' + CAST(@ErrorNumber AS VARCHAR(10));
    PRINT 'Error Severity: ' + CAST(@ErrorSeverity AS VARCHAR(10));
    PRINT 'Error State: ' + CAST(@ErrorState AS VARCHAR(10));
    PRINT 'Error Line: ' + CAST(@ErrorLine AS VARCHAR(10));
    PRINT 'Error Message: ' + @ErrorMessage;
    PRINT '';
    PRINT 'All changes have been rolled back. No data was modified.';
    PRINT 'Please review the error and retry the anonymization process.';
    
    -- No temporary objects to clean up - all processing done via direct UPDATE statements
    
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH

SET NOCOUNT OFF;

-- =====================================================================================================================
-- POST-ANONYMIZATION VERIFICATION QUERIES
-- =====================================================================================================================
-- Uncomment and run these queries after anonymization to verify the results:

/*
-- Verify Person table anonymization
SELECT TOP 5 FirstName, LastName, Email, Phone FROM Person;

-- Verify Customer table anonymization  
SELECT TOP 5 CompanyName, Email, ContactNumber FROM Customer;

-- Verify GOUser table anonymization
SELECT TOP 5 UserName, EmailAddress, FullName FROM [GOSecurity].[GOUser];

-- Check for any remaining potentially sensitive data patterns
SELECT 'Person' as TableName, COUNT(*) as Records,
       SUM(CASE WHEN Email LIKE '%@anonymized.local' THEN 1 ELSE 0 END) as AnonymizedEmails
FROM Person
WHERE Email IS NOT NULL
UNION ALL
SELECT 'Customer', COUNT(*), 
       SUM(CASE WHEN Email LIKE '%@anonymized.local' THEN 1 ELSE 0 END)
FROM Customer  
WHERE Email IS NOT NULL
UNION ALL
SELECT 'GOUser', COUNT(*),
       SUM(CASE WHEN EmailAddress LIKE '%@anonymized.local' THEN 1 ELSE 0 END)
FROM [GOSecurity].[GOUser]
WHERE EmailAddress IS NOT NULL;
*/
