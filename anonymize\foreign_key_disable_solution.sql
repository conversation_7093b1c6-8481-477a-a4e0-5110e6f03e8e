-- Alternative Solution: Temporarily Disable Foreign Key Constraints
-- This approach allows complete anonymization of audit trails but breaks referential relationships

-- Add this before Step 5 in your anonymization script:

-- =====================================================================================================================
-- TEMPORARILY DISABLE FOREIGN KEY CONSTRAINTS FOR CUSTOMERAUDIT TABLE
-- =====================================================================================================================

PRINT 'Temporarily disabling foreign key constraints for CustomerAudit table...';

-- Disable the specific foreign key constraint
IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name LIKE 'FK_CustomerAudit_GOUser%')
BEGIN
    DECLARE @constraintName NVARCHAR(128);
    SELECT @constraintName = name 
    FROM sys.foreign_keys 
    WHERE name LIKE 'FK_CustomerAudit_GOUser%';
    
    DECLARE @sql NVARCHAR(MAX) = 'ALTER TABLE [GOChangeTracking].[CustomerAudit] NOCHECK CONSTRAINT ' + @constraintName;
    EXEC sp_executesql @sql;
    PRINT 'Disabled constraint: ' + @constraintName;
END

-- Now proceed with the original CustomerAudit anonymization
UPDATE [GOChangeTracking].[CustomerAudit] SET
    CreatedBy = CASE WHEN CreatedBy IS NOT NULL THEN NEWID() ELSE NULL END,
    LastModifiedBy = CASE WHEN LastModifiedBy IS NOT NULL THEN NEWID() ELSE NULL END,
    DeletedBy = CASE WHEN DeletedBy IS NOT NULL THEN NEWID() ELSE NULL END;

-- Re-enable the foreign key constraint (this will fail if referential integrity is broken)
-- Comment out the next section if you want to permanently break the relationships
/*
IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name LIKE 'FK_CustomerAudit_GOUser%')
BEGIN
    DECLARE @constraintName2 NVARCHAR(128);
    SELECT @constraintName2 = name 
    FROM sys.foreign_keys 
    WHERE name LIKE 'FK_CustomerAudit_GOUser%';
    
    DECLARE @sql2 NVARCHAR(MAX) = 'ALTER TABLE [GOChangeTracking].[CustomerAudit] CHECK CONSTRAINT ' + @constraintName2;
    EXEC sp_executesql @sql2;
    PRINT 'Re-enabled constraint: ' + @constraintName2;
END
*/

PRINT 'CustomerAudit anonymization completed with constraints disabled.';
