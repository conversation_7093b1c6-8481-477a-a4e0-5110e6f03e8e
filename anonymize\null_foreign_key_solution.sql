-- Alternative Solution: Set Foreign Key Fields to NULL
-- This approach removes audit trail user references while maintaining table structure

-- Replace the CustomerAudit section in Step 5 with this:

-- Anonymize GOChangeTracking.CustomerAudit table by setting foreign keys to NULL
PRINT '  - Anonymizing GOChangeTracking.CustomerAudit table...';
IF EXISTS (SELECT 1 FROM sys.tables t JOIN sys.schemas s ON t.schema_id = s.schema_id 
           WHERE s.name = 'GOChangeTracking' AND t.name = 'CustomerAudit')
BEGIN
    -- Update CustomerAudit by setting all user references to NULL (removes audit trail links)
    UPDATE [GOChangeTracking].[CustomerAudit] SET
        CreatedBy = NULL,
        LastModifiedBy = NULL,
        DeletedBy = NULL;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOChangeTracking.CustomerAudit table (set user references to NULL).';
END
ELSE
BEGIN
    PRINT '    GOChangeTracking.CustomerAudit table not found - skipping.';
END
