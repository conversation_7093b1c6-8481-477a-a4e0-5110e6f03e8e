Primary Personal Information Tables:
Person Table:

FirstN<PERSON>, LastName - Names
Email - Email address
Phone - Phone number
Photo, PhotoFileSize, PhotoInter<PERSON>Name - Personal photos
Notes - May contain personal information
LastMedicalDate - Medical/health information

ContactPersonInformation Table:

FirstName, LastName - Names
Email - Email address
PhoneNo - Phone number
Address - Physical address

GOSecurity.GOUser Table:

UserName - <PERSON>rna<PERSON>
EmailAddress, NewEmailAddress - <PERSON>ail addresses
FullName, FirstName, LastName - Names
Password - Login credentials
ExternalUserId - External authentication ID

WebsiteUser Table:

Username - Login username
Password - Login credentials

Secondary Personal Information:
Customer Table:

ContactNumber - Phone number
Email - Email address
Addess [sic] - Physical address
CompanyName - May need anonymization depending on requirements

Card Table:

CardNumber - Access card identifier
Weigand - Card data
FacilityCode - Facility access code

LicenceDetail Table:

LicenseNumber - Driver's license number
Document - License document (may contain photo/personal info)

Module Table:

SimCardNumber - SIM card identifier
CCID - SIM card CCID
TechNumber - Technician identifier
Note - May contain personal information

GOSecurity.GOUser2FA Table:

OTPSecret - Two-factor authentication secret

GOSecurity.GOLoginHistory Table:

User - Username/identifier
Info - May contain IP addresses or other identifying information

Location/Activity Data:

GPSHistory and VehicleGPS tables: Latitude, Longitude - Location tracking
Session and related tables - User activity tracking
Impact table - Driving behavior data

Audit/Tracking Information:
GOChangeTracking.CustomerAudit:

CreatedBy, LastModifiedBy, DeletedBy - User identifiers

GOChangeTracking.GOChangeDelta:

Who - User making changes

Relationship Data:
These tables link to personal information and may need consideration:

AlertSubscription - Links to PersonId
EmailGroupsToPerson - Links to PersonId
MessageHistory - Links to GOUserId
ReportSubscription - Links to PersonId and GOUserId

To properly anonymize this database, you'll need to:

Replace all names with fake/random names
Replace emails with anonymized emails
Replace phone numbers with fake numbers
Remove or replace photo data
Anonymize addresses
Replace card numbers and license numbers
Hash or replace passwords
Consider anonymizing GPS coordinates and session data
Replace user IDs in audit trails while maintaining referential integrity